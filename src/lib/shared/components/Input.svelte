<script lang="ts">
    type dataType = "text" | "password" | "email" | "file" | "number";
    let {
        value = $bindable(),
        type = "text",
        placeholder,
        errorMessage,
        onChange,
    }: {
        value?: unknown;
        type?: dataType;
        placeholder: string;
        errorMessage?: string;
        onChange?: (event: Event) => unknown;
    } = $props();
</script>

<div class="min-h-fit w-full flex flex-col gap-1">
    <input
        class={errorMessage !== undefined
            ? "w-full bg-transparent placeholder:text-slate-400 text-white text-sm border border-danger-dark rounded-md px-3 py-2 focus:outline-none shadow-sm focus:shadow"
            : "w-full bg-transparent placeholder:text-slate-400 text-white text-sm border border-slate-200 rounded-md px-3 py-2 transition duration-300 ease focus:outline-none focus:border-primary hover:border-slate-300 shadow-sm focus:shadow"}
        {placeholder}
        {type}
        bind:value
        onchange={onChange}
    />
    <div class="h-5">
        {#if errorMessage !== undefined}
            <span class="text-sm text-danger">{errorMessage}</span>
        {/if}
    </div>
</div>
